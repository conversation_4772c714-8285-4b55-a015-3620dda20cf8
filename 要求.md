| 业务背景                                                                                                                                                     | 问题                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | 数据集修改 | 修改后数据集 |
| ------------------------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------- | ------------ |
| 4月某一周的某一天的Development部门的的XX用户提了大量工单                                                                                                     | 1. 【归因】为什么Development部门在2024年Q2期间平均处理周期显著高于其他部门？``                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |            |              |
| 4月某一周的某一天的XX部门的的XX用户提了一笔巨大的费用金额                                                                                                    | 1. 【归因+思路】2024年6月的财务费用金额为什么显著高于2024年5月？输出归因结论报告，这个报告的分析思路及需要包含的内容如下：   归因对象：财务费用金额   数据周期：观察期为2024年6月，对照期为2024年5月。   报告包含两部分：1.1费用结构变化分析，按「部门」拆解费用金额，筛选环比增长最大的5个非零部门，输出本期值、上期值、环比变化幅度和变化值，并对TOP5部门按「类别」「类型」进行二级拆解归因；1.2异常费用项目归因，描述整体费用本期值、上期值、环比变化幅度和变化值后，按「类别」「配置项」「地点」等维度拆解异常费用，结合「处理日期」「简短描述」总结核心变化。   输出要求：数据核对「金额」「处理日期」等关键字段，报告需包含1.1和1.2两部分，并重点标注无预算计划的异常增长项。                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |            |              |
| 你是负责企业效率的经营分析师，需要面向财务审批同学输出周度分析报告：帮助财务了解过去7天（（20240410-20240416））的审批效率变化情况，以说明反映审批效率变化。 | 1. 【开放+思路】你是负责企业效率的经营分析师，需要面向财务审批同学输出周度分析报告：帮助财务了解过去7天（（20240410-20240416））的审批效率变化情况，以说明反映审批效率变化。分析内容 ## 当周概况 ### 当周数据概况 按照[部门]、[类别]呈现当周日均审批量、平均处理周期、环比上周增长率，按日均审批量降序排列，使用表格展示，并解读数据 使用折线图，展现最近14天各[部门]、[类别]每天的单日审批量趋势图，并解读数据 按照[状态]、[类型]呈现当周平均处理周期、环比上周变化值，按处理周期降序排列，使用表格展示，并解读数据 使用折线图，展现最近14天各[状态]、[类型]每天的处理周期趋势图，并解读数据 ### 当周效率波动归因 使用表格，[部门]维度呈现当周平均处理周期、环比上周变化值，按处理周期降序排列，说明当周效率变化在部门维度上的主要原因 围绕每个处理周期环比变化≥1小时的部门维度 下钻到[用户]分析审批量，定位主要影响人员 下钻到[用户]+[类别]维度分析处理周期，定位主要影响的人员+费用类别维度 下钻到[用户]+[配置项]维度分析处理周期，定位主要影响的人员+配置项目维度 基于上述的多轮下钻，明确主要影响效率变化的人员+费用类别+配置项目，形成表格，并解读数据 # 字段使用## 核心指标 日均审批量 单日审批量 平均处理周期 ## 核心维度 ### 部门维度[部门]：申请部门分类 ### 人员维度 [用户]：实际申请人 ### 费用维度 [类别]：费用大类划分 [类型]：费用子类划分 [配置项]：费用明细项目 ### 审批维度 [状态]：审批状态 [处理周期]：创建到处理的时间差 ### 时间维度 当周：[创建时间] between '2024-04-10' AND '2024-04-16' 上周：[创建时间] between '2024-04-03' AND '2024-04-09' ### 其他分析维度 审批特征分布 [简短描述]关键字段分析 [金额]区间分布分析 |            |              |





| 增加2024年4月第2周Development部门，某用户提交大量工单的记录。`<br/>`       | `<br/>` | 首先，按照季度查看各个部门的平均处理周期。确认在2024年Q2期间Development部门平均处理周期确实显著高于其他部门。
然后，按照月份查看2024年Q2期间各个部门的平均处理周期，挖掘出Development部门平均处理周期显著高于其他部门的具体月份。通过数据输出结果库看到导致异常的月份为4月。然后，将月份限制在4月份进一步分析Development部门每周的具体工单情况。找出4月份平均处理周期出现异常的具体周，然后进一步分析出现异常具体的日期。

| 最后，分析这些平均处理周期出现异常日期的具体工单情况，                       |           |                                                                                                                                                                                                                      |
| ---------------------------------------------------------------------------- | --------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 增加2024年6月第2周Development部门的某用户提交多笔大额费用金额记录。`` | `` | 首先，按照月份查看财务费用金额。确认2024年6月的财务费用金额是否显著高于2024年5月。然后，按照部门查看2024年6月和2024年5月的财务费用金额情况，并筛选环比增长最大的5个非零部门，输出本期值、上期值、环比变化。`` |

例子

| 4月某一周的某一天的XX部门的的XX用户被拒绝大量XX类型工单（由于申请原因不明确导致拒绝通过） | 1. 【归因】2024年4月工单的拒绝率明显高于2024年3月，是什么原因导致这种情况？`` | 增加字段：拒绝原因`` | **df_merge21_final.csv**`` | 按月看一下按天看一下4月的工单拒绝率找到最高的天，按照部门去看XXX`` |
| ----------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------- | -------------------- | -------------------------------- | ------------------------------------------------------------------ |
